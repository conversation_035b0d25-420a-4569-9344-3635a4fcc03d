<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Potvrzení nahrání - Upload System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .confirmation-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .success-header {
            background: linear-gradient(135deg, #4a90e2, #357abd);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
        }
        
        .success-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #fff;
        }
        
        .success-title {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .success-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #4a90e2;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #4a90e2;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .details-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .details-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
            color: #333;
        }
        
        .details-content {
            padding: 20px;
        }
        
        .submission-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            min-width: 80px;
        }
        
        .info-value {
            color: #333;
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .category-list {
            list-style: none;
            padding: 0;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 600;
            color: #333;
        }
        
        .category-type {
            background: #e8f4fd;
            color: #4a90e2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .category-count {
            background: #4a90e2;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #4a90e2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #3a7bc8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="success-header">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1 class="success-title">Úspěšně nahráno!</h1>
            <p class="success-subtitle">Vaše data byla úspěšně odeslána do systému DA</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ stats.successful_urls }}</div>
                <div class="stat-label">URL adres</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.successful_files }}</div>
                <div class="stat-label">Souborů</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ stats.categories_with_data|length }}</div>
                <div class="stat-label">Kategorií</div>
            </div>
        </div>

        <div class="details-section">
            <div class="details-header">
                <i class="fas fa-info-circle"></i> Informace o odeslání
            </div>
            <div class="details-content">
                <div class="submission-info">
                    <div class="info-item">
                        <span class="info-label">PU:</span>
                        <span class="info-value">{{ submission.pu or 'Nezadáno' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">ID:</span>
                        <span class="info-value">{{ submission.submission_id[:8] }}...</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Čas:</span>
                        <span class="info-value">{{ submission.timestamp[:19].replace('T', ' ') }}</span>
                    </div>
                </div>
            </div>
        </div>

        {% if stats.categories_with_data %}
        <div class="details-section">
            <div class="details-header">
                <i class="fas fa-list"></i> Nahrané kategorie
            </div>
            <div class="details-content">
                <ul class="category-list">
                    {% for category in stats.categories_with_data %}
                    <li class="category-item">
                        <div>
                            <span class="category-type">{{ category.type }}</span>
                            <span class="category-name">{{ category.category }}</span>
                        </div>
                        <span class="category-count">{{ category.count }}</span>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% else %}
        <div class="details-section">
            <div class="details-content">
                <div class="no-data">Žádná data nebyla nahrána</div>
            </div>
        </div>
        {% endif %}

        <div class="action-buttons">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nahrát další data
            </a>
            <a href="/submissions" class="btn btn-secondary">
                <i class="fas fa-list"></i> Zobrazit všechna odeslání
            </a>
        </div>
    </div>
</body>
</html>
