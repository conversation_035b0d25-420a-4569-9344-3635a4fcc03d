document.addEventListener('DOMContentLoaded', function() {
    // Maximum number of URL inputs per section
    const MAX_URL_INPUTS = 10;

    // Add URL validation function
    function isValidUrl(url) {
        if (!url || url.trim() === '') return false;

        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }

    // Function to create a new URL input field
    function createUrlInput() {
        const input = document.createElement('input');
        input.type = 'url';
        input.className = 'url-input';
        input.placeholder = 'Vložte URL adresu';
        return input;
    }

    // Function to handle "Add URL" button click
    function handleAddUrlClick(e) {
        const button = e.currentTarget;
        const containerId = button.getAttribute('data-container');
        const container = document.getElementById(`urlContainer${containerId}`);
        const urlInputsDiv = container.querySelector('.url-inputs');
        const currentInputs = urlInputsDiv.querySelectorAll('.url-input');

        // Check if we've reached the maximum number of inputs
        if (currentInputs.length >= MAX_URL_INPUTS) {
            button.disabled = true;
            return;
        }

        // Add a new input field
        const newInput = createUrlInput();
        urlInputsDiv.appendChild(newInput);

        // Focus the new input
        newInput.focus();

        // Disable the button if we've reached the maximum
        if (currentInputs.length + 1 >= MAX_URL_INPUTS) {
            button.disabled = true;
        }
    }

    // Add event listeners to all "Add URL" buttons
    const addUrlButtons = document.querySelectorAll('.add-url-btn');
    addUrlButtons.forEach(button => {
        button.addEventListener('click', handleAddUrlClick);
    });

    // Handle file input changes
    const fileInputs = document.querySelectorAll('.file-input');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const label = this.nextElementSibling;
            if (this.files && this.files.length > 0) {
                // Add a class to indicate a file is selected
                this.classList.add('has-file');
                // Update the label text to show the file name
                label.textContent = this.files[0].name;
            } else {
                // Remove the class if no file is selected
                this.classList.remove('has-file');
                // Reset the label text
                label.textContent = 'Vyberte soubor';
            }
        });
    });

    // Handle form submission
    const submitButton = document.getElementById('submitButton');
    submitButton.addEventListener('click', function() {
        // Collect data from all sections
        const formData = {
            urls: [],
            files: []
        };
        let hasInvalidUrls = false;

        // Process URL inputs
        for (let i = 1; i <= 4; i++) {
            const dropdown = document.getElementById(`dropdown${i}`);
            const urlInputs = document.querySelectorAll(`#urlContainer${i} .url-input`);

            // Get selected option from dropdown
            const selectedOption = dropdown.options[dropdown.selectedIndex].value;
            const selectedText = dropdown.options[dropdown.selectedIndex].text;

            // Get URLs from input fields
            const validUrls = [];
            const invalidUrls = [];

            urlInputs.forEach(input => {
                const url = input.value.trim();
                if (url !== '') {
                    if (isValidUrl(url)) {
                        validUrls.push(url);
                    } else {
                        invalidUrls.push(url);
                        hasInvalidUrls = true;
                    }
                }
            });

            formData.urls.push({
                category: selectedOption,
                categoryName: selectedText,
                validUrls: validUrls,
                invalidUrls: invalidUrls
            });
        }

        // Process file inputs
        for (let i = 1; i <= 4; i++) {
            const dropdown = document.getElementById(`dropdown${i+4}`);
            const fileInputs = document.querySelectorAll(`#fileContainer${i} .file-input`);

            // Get selected option from dropdown
            const selectedOption = dropdown.options[dropdown.selectedIndex].value;
            const selectedText = dropdown.options[dropdown.selectedIndex].text;

            // Get files from input fields
            const files = [];

            fileInputs.forEach(input => {
                if (input.files && input.files.length > 0) {
                    files.push({
                        name: input.files[0].name,
                        size: input.files[0].size,
                        type: input.files[0].type
                    });
                }
            });

            formData.files.push({
                category: selectedOption,
                categoryName: selectedText,
                files: files
            });
        }

        // Check if there are any invalid URLs
        if (hasInvalidUrls) {
            // Create a message with invalid URLs
            let message = "Některé URL adresy nejsou platné:\n\n";

            formData.urls.forEach(section => {
                if (section.invalidUrls.length > 0) {
                    message += `Sekce "${section.categoryName}":\n`;
                    section.invalidUrls.forEach(url => {
                        message += `- ${url}\n`;
                    });
                    message += "\n";
                }
            });

            message += "Chcete pokračovat pouze s platnými URL adresami?";

            // Ask user if they want to continue with valid URLs only
            if (confirm(message)) {
                // Continue with valid URLs only
                console.log('Form data:', formData);
                alert('Data byla odeslána!');
            }
        } else {
            // All URLs are valid, proceed
            console.log('Form data:', formData);
            alert('Data byla odeslána!');
        }
    });

    // Add paste handler for URL inputs
    document.addEventListener('paste', function(e) {
        // Check if the target is a URL input
        if (e.target.classList.contains('url-input')) {
            const input = e.target;
            const container = input.closest('.url-input-container');
            const urlInputsDiv = container.querySelector('.url-inputs');
            const addButton = container.querySelector('.add-url-btn');
            const currentInputs = urlInputsDiv.querySelectorAll('.url-input');

            // Get the pasted text
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');

            // Split the pasted text by newlines, commas, or spaces
            const urls = pastedText.split(/[\n,\s]+/).filter(url => url.trim() !== '');

            // If there's only one URL or we're at max capacity, let the default paste happen
            if (urls.length <= 1 || currentInputs.length >= MAX_URL_INPUTS) {
                return;
            }

            // Prevent the default paste
            e.preventDefault();

            // Fill the current input with the first URL
            input.value = urls[0];

            // Create new inputs for the remaining URLs (up to the maximum)
            for (let i = 1; i < urls.length && currentInputs.length + i < MAX_URL_INPUTS; i++) {
                const newInput = createUrlInput();
                newInput.value = urls[i];
                urlInputsDiv.appendChild(newInput);
            }

            // Disable the add button if we've reached the maximum
            if (urlInputsDiv.querySelectorAll('.url-input').length >= MAX_URL_INPUTS) {
                addButton.disabled = true;
            }
        }
    });
});
