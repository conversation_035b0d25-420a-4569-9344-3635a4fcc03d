document.addEventListener('DOMContentLoaded', function() {
    // Reference to all textareas
    const textareas = document.querySelectorAll('.url-input-area textarea');

    // Function to handle pasting URLs
    function handlePaste(e, textarea) {
        // Prevent default paste behavior
        e.preventDefault();

        // Get pasted text
        const pastedText = (e.clipboardData || window.clipboardData).getData('text');

        // Process pasted text - split by any whitespace or commas
        const urls = pastedText.split(/[\s,]+/).filter(url => url.trim() !== '');

        // Get current cursor position
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // Get current value of textarea
        const value = textarea.value;

        // Insert each URL on a new line
        const insertText = urls.join('\n');
        textarea.value = value.substring(0, start) + insertText + value.substring(end);

        // Move cursor after inserted text
        textarea.selectionStart = textarea.selectionEnd = start + insertText.length;
    }

    // Add event listeners to handle line-by-line input
    textareas.forEach(textarea => {
        // Handle Enter key press
        textarea.addEventListener('keydown', function(e) {
            // Check if Enter key was pressed
            if (e.key === 'Enter') {
                // Get current cursor position
                const start = this.selectionStart;
                const end = this.selectionEnd;

                // Get current value of textarea
                const value = this.value;

                // Check if the current line is empty or contains only whitespace
                const currentLineStart = value.lastIndexOf('\n', start - 1) + 1;
                const currentLine = value.substring(currentLineStart, start);

                if (currentLine.trim() === '') {
                    // If current line is empty, don't add another empty line
                    e.preventDefault();
                    return;
                }

                // Add new line
                this.value = value.substring(0, start) + '\n' + value.substring(end);

                // Move cursor to the beginning of the new line
                this.selectionStart = this.selectionEnd = start + 1;

                // Prevent default Enter behavior
                e.preventDefault();
            }
        });

        // Handle paste event
        textarea.addEventListener('paste', function(e) {
            handlePaste(e, this);
        });

        // Add placeholder text that encourages one URL per line
        textarea.placeholder = "Vložte URL adresy - každou na samostatný řádek";
    });

    // Add URL validation function
    function isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }

    // Handle form submission
    const submitButton = document.getElementById('submitButton');
    submitButton.addEventListener('click', function() {
        // Collect data from all sections
        const formData = [];
        let hasInvalidUrls = false;

        for (let i = 1; i <= 4; i++) {
            const dropdown = document.getElementById(`dropdown${i}`);
            const textarea = document.querySelector(`#urlArea${i} textarea`);

            // Get selected option from dropdown
            const selectedOption = dropdown.options[dropdown.selectedIndex].value;
            const selectedText = dropdown.options[dropdown.selectedIndex].text;

            // Get URLs from textarea (split by new line and filter empty lines)
            const urlLines = textarea.value
                .split('\n')
                .map(url => url.trim())
                .filter(url => url !== '');

            // Validate URLs
            const validUrls = [];
            const invalidUrls = [];

            urlLines.forEach(url => {
                if (isValidUrl(url)) {
                    validUrls.push(url);
                } else {
                    invalidUrls.push(url);
                    hasInvalidUrls = true;
                }
            });

            formData.push({
                category: selectedOption,
                categoryName: selectedText,
                validUrls: validUrls,
                invalidUrls: invalidUrls
            });
        }

        // Check if there are any invalid URLs
        if (hasInvalidUrls) {
            // Create a message with invalid URLs
            let message = "Některé URL adresy nejsou platné:\n\n";

            formData.forEach(section => {
                if (section.invalidUrls.length > 0) {
                    message += `Sekce "${section.categoryName}":\n`;
                    section.invalidUrls.forEach(url => {
                        message += `- ${url}\n`;
                    });
                    message += "\n";
                }
            });

            message += "Chcete pokračovat pouze s platnými URL adresami?";

            // Ask user if they want to continue with valid URLs only
            if (confirm(message)) {
                // Continue with valid URLs only
                console.log('Form data (valid URLs only):', formData);
                alert('Data byla odeslána!');
            }
        } else {
            // All URLs are valid, proceed
            console.log('Form data:', formData);
            alert('Data byla odeslána!');
        }
    });
});
