document.addEventListener('DOMContentLoaded', function() {
    // Reference to all textareas
    const textareas = document.querySelectorAll('.url-input-area textarea');
    
    // Add event listeners to handle line-by-line input
    textareas.forEach(textarea => {
        textarea.addEventListener('keydown', function(e) {
            // Check if Enter key was pressed
            if (e.key === 'Enter') {
                // Get current cursor position
                const start = this.selectionStart;
                const end = this.selectionEnd;
                
                // Get current value of textarea
                const value = this.value;
                
                // Check if the current line is empty or contains only whitespace
                const currentLineStart = value.lastIndexOf('\n', start - 1) + 1;
                const currentLine = value.substring(currentLineStart, start);
                
                if (currentLine.trim() === '') {
                    // If current line is empty, don't add another empty line
                    e.preventDefault();
                    return;
                }
                
                // Add new line
                this.value = value.substring(0, start) + '\n' + value.substring(end);
                
                // Move cursor to the beginning of the new line
                this.selectionStart = this.selectionEnd = start + 1;
                
                // Prevent default Enter behavior
                e.preventDefault();
            }
        });
    });
    
    // Handle form submission
    const submitButton = document.getElementById('submitButton');
    submitButton.addEventListener('click', function() {
        // Collect data from all sections
        const formData = [];
        
        for (let i = 1; i <= 4; i++) {
            const dropdown = document.getElementById(`dropdown${i}`);
            const textarea = document.querySelector(`#urlArea${i} textarea`);
            
            // Get selected option from dropdown
            const selectedOption = dropdown.options[dropdown.selectedIndex].value;
            
            // Get URLs from textarea (split by new line and filter empty lines)
            const urls = textarea.value
                .split('\n')
                .map(url => url.trim())
                .filter(url => url !== '');
            
            formData.push({
                category: selectedOption,
                urls: urls
            });
        }
        
        // Log the collected data (in a real application, you would send this to a server)
        console.log('Form data:', formData);
        alert('Data byla odeslána!');
    });
});
