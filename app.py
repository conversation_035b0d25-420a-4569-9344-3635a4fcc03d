from flask import Flask, render_template, request, jsonify
import os
import json
import base64
from datetime import datetime
from werkzeug.utils import secure_filename
import uuid

app = Flask(__name__)

# Konfigurace
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['SECRET_KEY'] = 'your-secret-key-here'

# Povolené přípony souborů
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html', pu=None)

@app.route('/<pu>')
def index_with_pu(pu):
    return render_template('index.html', pu=pu)

@app.route('/submit', methods=['POST'])
def submit_data():
    try:
        # Získání dat z formuláře
        pu = request.form.get('pu', '').strip()

        form_data = {
            'timestamp': datetime.now().isoformat(),
            'submission_id': str(uuid.uuid4()),
            'pu': pu,
            'urls': [],
            'files': []
        }

        # Zpracování URL adres
        for i in range(1, 5):  # 4 kategorie pro URL
            category_key = f'dropdown{i}'
            category = request.form.get(category_key, '')

            urls = []
            # Získání všech URL polí pro tuto kategorii
            for key in request.form.keys():
                if key.startswith(f'url_{i}_'):
                    url_value = request.form.get(key, '').strip()
                    if url_value:
                        urls.append(url_value)

            if urls:  # Pouze pokud jsou nějaké URL adresy
                form_data['urls'].append({
                    'category': category,
                    'urls': urls
                })

        # Zpracování souborů (ukládání pouze do paměti)
        for i in range(1, 5):  # 4 kategorie pro soubory
            category_key = f'dropdown{i+4}'
            category = request.form.get(category_key, '')

            files = []
            # Získání všech souborů pro tuto kategorii
            for key in request.files.keys():
                if key.startswith(f'file_{i}_'):
                    file = request.files[key]
                    if file and file.filename != '' and allowed_file(file.filename):
                        # Bezpečné jméno souboru
                        filename = secure_filename(file.filename)

                        # Čtení obsahu souboru do paměti
                        file_content = file.read()
                        file_size = len(file_content)

                        # Kódování obsahu souboru do base64 pro uložení do JSON
                        file_content_base64 = base64.b64encode(file_content).decode('utf-8')

                        files.append({
                            'original_name': filename,
                            'file_size': file_size,
                            'file_type': file.content_type or 'application/octet-stream',
                            'file_content_base64': file_content_base64,
                            'upload_timestamp': datetime.now().isoformat()
                        })

            if files:  # Pouze pokud jsou nějaké soubory
                form_data['files'].append({
                    'category': category,
                    'files': files
                })



        # Odpověď pro AJAX
        return jsonify({
            'success': True,
            'message': 'Data byla úspěšně odeslána!',
            'submission_id': form_data['submission_id'],
            'data': form_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Chyba při zpracování dat: {str(e)}'
        }), 500

@app.route('/submissions')
def list_submissions():
    """Zobrazení všech odeslaných dat"""
    submissions = []
    data_dir = 'data'

    if os.path.exists(data_dir):
        for filename in os.listdir(data_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(data_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        submissions.append(data)
                except Exception as e:
                    print(f"Chyba při čtení {filename}: {e}")

    # Seřazení podle času
    submissions.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

    return render_template('submissions.html', submissions=submissions)

# Funkce pro stahování souborů byla odstraněna - soubory se ukládají pouze do paměti

if __name__ == '__main__':
    # Pro produkční použití na Windows Server 2019
    app.run(host='0.0.0.0', port=8087, debug=False)
