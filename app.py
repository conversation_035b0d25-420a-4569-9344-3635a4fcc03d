from flask import Flask, render_template, request, jsonify
import os
import json
import base64
from datetime import datetime
from werkzeug.utils import secure_filename
import uuid
import requests
import io

app = Flask(__name__)

# Konfigurace
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['SECRET_KEY'] = 'your-secret-key-here'

# Povolené přípony souborů
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar'}


def upload_da(file, doctype="", caseno="", subcaseno=""):
    """Test the upload endpoint with a sample file."""
    try:
        file_like = file
        
        # Prepare the form data
        files = {'file': ('soubor.pdf', file_like, 'application/pdf')}
        data = {}
        
        if doctype:
            data['doctype'] = doctype
        if caseno:
            data['caseno'] = caseno
        if subcaseno:
            data['subcaseno'] = subcaseno
        
        # Send the request
        response = requests.post('http://czsrpaappt3.cpas.cz:8085/upload', files=files, data=data)
        print(f"Upload response: {response.status_code}")
        print(json.dumps(response.json(), indent=2))
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing upload: {str(e)}")
        return False

def url_to_pdf(base_url, url_to_convert, output_path=None, disable_images=True, scale=0.8):
    """
    Testuje PDF Web Service.
    
    Args:
        base_url (str): Základní URL služby (např. http://localhost:5000)
        url_to_convert (str): URL stránky ke konverzi
        output_path (str, optional): Cesta k výstupnímu PDF souboru
        disable_images (bool): Zda vypnout načítání obrázků
        scale (float): Měřítko pro PDF
        
    Returns:
        bool: True pokud test proběhl úspěšně, jinak False
    """
    # Generování PDF
    try:
        pdf_url = f"{base_url}/pdf?url={url_to_convert}&disable_images={str(disable_images).lower()}&scale={scale}"
        print(f"Generování PDF: {pdf_url}")
        
        pdf_response = requests.get(pdf_url)
        pdf_response.raise_for_status()

        return io.BufferedReader(io.BytesIO(pdf_response.content))
    
    except Exception as e:
        print(f"Chyba při generování PDF: {e}")
        return False

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html', pu=None)

@app.route('/<pu>')
def index_with_pu(pu):
    return render_template('index.html', pu=pu)

@app.route('/submit', methods=['POST'])
def submit_data():
    try:
        # Získání dat z formuláře
        pu = request.form.get('pu', '').strip()

        form_data = {
            'timestamp': datetime.now().isoformat(),
            'submission_id': str(uuid.uuid4()),
            'pu': pu,
            'urls': [],
            'files': []
        }

        # Zpracování URL adres
        for i in range(1, 5):  # 4 kategorie pro URL
            category_key = f'dropdown{i}'
            category = request.form.get(category_key, '')

            urls = []
            # Získání všech URL polí pro tuto kategorii
            for key in request.form.keys():
                if key.startswith(f'url_{i}_'):
                    url_value = request.form.get(key, '').strip()
                    if url_value:
                        urls.append(url_value)

            if urls:  # Pouze pokud jsou nějaké URL adresy
                form_data['urls'].append({
                    'category': category,
                    'urls': urls
                })

        # Zpracování souborů (ukládání pouze do paměti)
        for i in range(1, 5):  # 4 kategorie pro soubory
            category_key = f'dropdown{i+4}'
            category = request.form.get(category_key, '')

            files = []
            # Získání všech souborů pro tuto kategorii
            for key in request.files.keys():
                if key.startswith(f'file_{i}_'):
                    file = request.files[key]
                    if file and file.filename != '' and allowed_file(file.filename):
                        # Bezpečné jméno souboru
                        filename = secure_filename(file.filename)

                        # Čtení obsahu souboru do paměti
                        file_content = file.read()
                        file_size = len(file_content)

                        # Kódování obsahu souboru do base64 pro uložení do JSON
                        file_content_base64 = base64.b64encode(file_content).decode('utf-8')

                        files.append({
                            'original_name': filename,
                            'file_size': file_size,
                            'file_type': file.content_type or 'application/octet-stream',
                            'file_content_base64': file_content_base64,
                            'upload_timestamp': datetime.now().isoformat()
                        })

            if files:  # Pouze pokud jsou nějaké soubory
                form_data['files'].append({
                    'category': category,
                    'files': files
                })

        # Kontrola, zda jsou nějaká data k nahrání
        has_urls = any(section['urls'] for section in form_data['urls'])
        has_files = any(section['files'] for section in form_data['files'])

        if not has_urls and not has_files:
            return jsonify({
                'success': False,
                'message': 'Nejsou žádná data k nahrání. Zadejte alespoň jednu URL adresu nebo vyberte soubor.'
            }), 400

        # Uložení dat do JSON souboru pro další zpracování
        data_filename = f"submission_{form_data['submission_id']}.json"
        data_path = os.path.join('data', data_filename)

        # Vytvoření složky pro data
        if not os.path.exists('data'):
            os.makedirs('data')

        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(form_data, f, ensure_ascii=False, indent=2)

        
        
        # Odpověď pro AJAX
        return jsonify({
            'success': True,
            'message': 'Data byla úspěšně odeslána!',
            'submission_id': form_data['submission_id'],
            'redirect_url': f'/confirmation/{form_data["submission_id"]}',
            'data': form_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Chyba při zpracování dat: {str(e)}'
        }), 500

@app.route('/submissions')
def list_submissions():
    """Zobrazení všech odeslaných dat"""
    submissions = []
    data_dir = 'data'

    if os.path.exists(data_dir):
        for filename in os.listdir(data_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(data_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        submissions.append(data)
                except Exception as e:
                    print(f"Chyba při čtení {filename}: {e}")

    # Seřazení podle času
    submissions.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

    return render_template('submissions.html', submissions=submissions)

@app.route('/confirmation/<submission_id>')
def confirmation(submission_id):
    """Zobrazení potvrzení o úspěšném nahrání"""
    data_path = os.path.join('data', f'submission_{submission_id}.json')

    if not os.path.exists(data_path):
        return "Odeslání nenalezeno", 404

    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            submission_data = json.load(f)

        # Zpracování statistik
        stats = {
            'total_urls': 0,
            'total_files': 0,
            'successful_urls': 0,
            'successful_files': 0,
            'failed_urls': [],
            'categories_with_data': []
        }

        # Zpracování URL adres
        for url_section in submission_data.get('urls', []):
            if url_section.get('urls'):
                stats['total_urls'] += len(url_section['urls'])
                stats['successful_urls'] += len(url_section['urls'])
                stats['categories_with_data'].append({
                    'type': 'URL',
                    'category': url_section['category'],
                    'count': len(url_section['urls'])
                })

        # Zpracování souborů
        for file_section in submission_data.get('files', []):
            if file_section.get('files'):
                stats['total_files'] += len(file_section['files'])
                stats['successful_files'] += len(file_section['files'])
                stats['categories_with_data'].append({
                    'type': 'Soubor',
                    'category': file_section['category'],
                    'count': len(file_section['files'])
                })

        return render_template('confirmation.html',
                             submission=submission_data,
                             stats=stats)

    except Exception as e:
        return f"Chyba při načítání dat: {str(e)}", 500

# Funkce pro stahování souborů byla odstraněna - soubory se ukládají pouze do paměti

if __name__ == '__main__':
    # Pro produkční použití na Windows Server 2019
    app.run(host='0.0.0.0', port=8087, debug=False)
