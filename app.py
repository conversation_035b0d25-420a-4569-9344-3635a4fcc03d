from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory
import os
import json
from datetime import datetime
from werkzeug.utils import secure_filename
import uuid

app = Flask(__name__)

# Konfigurace
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['SECRET_KEY'] = 'your-secret-key-here'

# Povolené přípony souborů
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar'}

# Vytvoření slo<PERSON>ky pro nahrané soubory
if not os.path.exists(app.config['UPLOAD_FOLDER']):
    os.makedirs(app.config['UPLOAD_FOLDER'])

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/submit', methods=['POST'])
def submit_data():
    try:
        # Získání dat z formuláře
        form_data = {
            'timestamp': datetime.now().isoformat(),
            'submission_id': str(uuid.uuid4()),
            'urls': [],
            'files': []
        }

        # Zpracování URL adres
        for i in range(1, 5):  # 4 kategorie pro URL
            category_key = f'dropdown{i}'
            category = request.form.get(category_key, '')

            urls = []
            # Získání všech URL polí pro tuto kategorii
            for key in request.form.keys():
                if key.startswith(f'url_{i}_'):
                    url_value = request.form.get(key, '').strip()
                    if url_value:
                        urls.append(url_value)

            if urls:  # Pouze pokud jsou nějaké URL adresy
                form_data['urls'].append({
                    'category': category,
                    'urls': urls
                })

        # Zpracování souborů
        for i in range(1, 5):  # 4 kategorie pro soubory
            category_key = f'dropdown{i+4}'
            category = request.form.get(category_key, '')

            files = []
            # Získání všech souborů pro tuto kategorii
            for key in request.files.keys():
                if key.startswith(f'file_{i}_'):
                    file = request.files[key]
                    if file and file.filename != '' and allowed_file(file.filename):
                        # Bezpečné jméno souboru
                        filename = secure_filename(file.filename)
                        # Přidání timestamp pro jedinečnost
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        unique_filename = f"{timestamp}_{filename}"

                        # Uložení souboru
                        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
                        file.save(file_path)

                        files.append({
                            'original_name': filename,
                            'saved_name': unique_filename,
                            'file_path': file_path,
                            'file_size': os.path.getsize(file_path)
                        })

            if files:  # Pouze pokud jsou nějaké soubory
                form_data['files'].append({
                    'category': category,
                    'files': files
                })

        # Uložení dat do JSON souboru pro další zpracování
        data_filename = f"submission_{form_data['submission_id']}.json"
        data_path = os.path.join('data', data_filename)

        # Vytvoření složky pro data
        if not os.path.exists('data'):
            os.makedirs('data')

        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(form_data, f, ensure_ascii=False, indent=2)

        # Odpověď pro AJAX
        return jsonify({
            'success': True,
            'message': 'Data byla úspěšně odeslána!',
            'submission_id': form_data['submission_id'],
            'data': form_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Chyba při zpracování dat: {str(e)}'
        }), 500

@app.route('/submissions')
def list_submissions():
    """Zobrazení všech odeslaných dat"""
    submissions = []
    data_dir = 'data'

    if os.path.exists(data_dir):
        for filename in os.listdir(data_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(data_dir, filename)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        submissions.append(data)
                except Exception as e:
                    print(f"Chyba při čtení {filename}: {e}")

    # Seřazení podle času
    submissions.sort(key=lambda x: x.get('timestamp', ''), reverse=True)

    return render_template('submissions.html', submissions=submissions)

@app.route('/download/<filename>')
def download_file(filename):
    """Stažení nahraného souboru"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    # Pro produkční použití na Windows Server 2019
    app.run(host='0.0.0.0', port=8087, debug=False)
