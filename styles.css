* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    padding: 30px;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

header {
    margin-bottom: 30px;
    text-align: center;
}

.logo {
    margin-bottom: 20px;
}

.logo-text {
    color: #e30613;
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

header h1 {
    color: #e30613;
    margin-bottom: 10px;
    font-weight: 600;
}

header p {
    color: #666;
    font-size: 16px;
}

footer {
    margin-top: 40px;
    text-align: center;
    color: #777;
    font-size: 14px;
    font-style: italic;
}

.dropdowns-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    margin-bottom: 50px;
}

.dropdown-section {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.dropdown-section label {
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
    font-size: 15px;
}

.dropdown {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.dropdown:focus, .dropdown:hover {
    border-color: #e30613;
    outline: none;
    box-shadow: 0 2px 8px rgba(227, 6, 19, 0.2);
}

.url-input-container {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    padding: 15px;
    border: 1px solid #ddd;
}

.url-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.url-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    color: #555;
    transition: all 0.3s ease;
}

.url-input:focus {
    outline: none;
    border-color: #e30613;
    box-shadow: 0 0 0 2px rgba(227, 6, 19, 0.2);
}

.url-input::placeholder {
    color: #aaa;
}

.add-url-btn, .add-file-btn {
    background-color: #fff0f0;
    color: #e30613;
    border: 1px dashed #e30613;
    border-radius: 6px;
    padding: 10px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 10px;
}

.add-url-btn:hover, .add-file-btn:hover {
    background-color: #ffe0e0;
}

.add-url-btn:disabled, .add-file-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.section-title {
    margin: 40px 0 20px;
    color: #e30613;
    font-weight: 600;
    text-align: center;
    font-size: 24px;
}

.file-input-container {
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    padding: 15px;
    border: 1px solid #ddd;
}

.file-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.file-input-wrapper {
    position: relative;
    overflow: hidden;
}

.file-input {
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 2;
}

.file-label {
    display: block;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    text-align: center;
    color: #555;
    font-size: 14px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-input:hover + .file-label {
    background-color: #f0f0f0;
    border-color: #ccc;
}

.file-input:focus + .file-label {
    outline: none;
    border-color: #e30613;
    box-shadow: 0 0 0 2px rgba(227, 6, 19, 0.2);
}

/* Style for when a file is selected */
.file-input:not([value=""]) + .file-label,
.file-input.has-file + .file-label {
    background-color: #ffe0e0;
    border-color: #e30613;
    color: #e30613;
}

.submit-container {
    margin-top: 30px;
    display: flex;
    justify-content: flex-start;
}

#submitButton {
    padding: 15px 35px;
    background-color: #e30613;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

#submitButton:hover {
    background-color: #c00510;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

#submitButton:active {
    transform: translateY(0);
}

@media (max-width: 1100px) {
    .dropdowns-container {
        flex-direction: column;
    }

    .dropdown-section {
        width: 100%;
    }
}
