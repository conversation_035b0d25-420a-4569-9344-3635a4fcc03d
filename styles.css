* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    padding: 30px;
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

header {
    margin-bottom: 30px;
    text-align: center;
}

header h1 {
    color: #4a90e2;
    margin-bottom: 10px;
    font-weight: 600;
}

header p {
    color: #666;
    font-size: 16px;
}

footer {
    margin-top: 40px;
    text-align: center;
    color: #777;
    font-size: 14px;
    font-style: italic;
}

.dropdowns-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    margin-bottom: 50px;
}

.dropdown-section {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.dropdown-section label {
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
    font-size: 15px;
}

.dropdown {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.dropdown:focus, .dropdown:hover {
    border-color: #4a90e2;
    outline: none;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.url-input-area {
    width: 100%;
    height: 220px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    overflow: hidden;
}

.url-input-area textarea {
    width: 100%;
    height: 100%;
    padding: 15px;
    border: none;
    resize: none;
    font-size: 14px;
    line-height: 1.6;
    color: #555;
}

.url-input-area textarea:focus {
    outline: none;
}

.url-input-area textarea::placeholder {
    color: #aaa;
}

.submit-container {
    margin-top: 30px;
    display: flex;
    justify-content: flex-start;
}

#submitButton {
    padding: 15px 35px;
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

#submitButton:hover {
    background-color: #3a7bc8;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

#submitButton:active {
    transform: translateY(0);
}

@media (max-width: 1100px) {
    .dropdowns-container {
        flex-direction: column;
    }

    .dropdown-section {
        width: 100%;
    }
}
