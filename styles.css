* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    padding: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.dropdowns-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 40px;
}

.dropdown-section {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dropdown {
    width: 100%;
    padding: 15px;
    border: 2px solid #000;
    background-color: #fff;
    font-size: 16px;
    cursor: pointer;
}

.url-input-area {
    width: 100%;
    height: 200px;
    border: 2px solid #000;
}

.url-input-area textarea {
    width: 100%;
    height: 100%;
    padding: 10px;
    border: none;
    resize: none;
    font-size: 14px;
}

.submit-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-start;
}

#submitButton {
    padding: 15px 30px;
    background-color: #fff;
    border: 2px solid #000;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#submitButton:hover {
    background-color: #f0f0f0;
}

@media (max-width: 1100px) {
    .dropdowns-container {
        flex-direction: column;
    }
    
    .dropdown-section {
        width: 100%;
    }
}
