@echo off
echo Spouštím Upload Server...
echo.

REM Kontrola, zda existuje Python
python --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: Python není nainstalován nebo není v PATH!
    echo Nainstalujte Python z https://python.org
    pause
    exit /b 1
)

REM Instalace závislostí
echo Instaluji závislosti...
pip install -r requirements.txt

REM Spuštění serveru
echo.
echo Spouštím Flask server...
echo Server bude dostupný na: http://localhost:8087
echo Pro zastavení serveru stiskněte Ctrl+C
echo.

python app.py

pause
