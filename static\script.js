document.addEventListener('DOMContentLoaded', function() {
    // Maximum number of inputs per section
    const MAX_INPUTS = 10;
    
    // Add URL validation function
    function isValidUrl(url) {
        if (!url || url.trim() === '') return false;
        
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    }
    
    // Function to create a new URL input field
    function createUrlInput() {
        const input = document.createElement('input');
        input.type = 'url';
        input.className = 'url-input';
        input.placeholder = 'Vložte URL adresu';
        return input;
    }
    
    // Function to create a new file input field
    function createFileInput(containerId) {
        // Create wrapper div
        const wrapper = document.createElement('div');
        wrapper.className = 'file-input-wrapper';
        
        // Create unique ID for the file input
        const fileInputs = document.querySelectorAll('.file-input');
        const newId = `file${fileInputs.length + 1}`;
        
        // Create file input
        const input = document.createElement('input');
        input.type = 'file';
        input.className = 'file-input';
        input.id = newId;
        
        // Create label
        const label = document.createElement('label');
        label.htmlFor = newId;
        label.className = 'file-label';
        label.textContent = 'Vyberte soubor';
        
        // Add change event listener
        input.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                this.classList.add('has-file');
                label.textContent = this.files[0].name;
            } else {
                this.classList.remove('has-file');
                label.textContent = 'Vyberte soubor';
            }
        });
        
        // Append elements to wrapper
        wrapper.appendChild(input);
        wrapper.appendChild(label);
        
        return wrapper;
    }
    
    // Function to handle "Add URL" button click
    function handleAddUrlClick(e) {
        const button = e.currentTarget;
        const containerId = button.getAttribute('data-container');
        const container = document.getElementById(`urlContainer${containerId}`);
        const urlInputsDiv = container.querySelector('.url-inputs');
        const currentInputs = urlInputsDiv.querySelectorAll('.url-input');
        
        // Check if we've reached the maximum number of inputs
        if (currentInputs.length >= MAX_INPUTS) {
            button.disabled = true;
            return;
        }
        
        // Add a new input field
        const newInput = createUrlInput();
        urlInputsDiv.appendChild(newInput);
        
        // Focus the new input
        newInput.focus();
        
        // Disable the button if we've reached the maximum
        if (currentInputs.length + 1 >= MAX_INPUTS) {
            button.disabled = true;
        }
    }
    
    // Function to handle "Add File" button click
    function handleAddFileClick(e) {
        const button = e.currentTarget;
        const containerId = button.getAttribute('data-container');
        const container = document.getElementById(`fileContainer${containerId}`);
        const fileInputsDiv = container.querySelector('.file-inputs');
        const currentInputs = fileInputsDiv.querySelectorAll('.file-input-wrapper');
        
        // Check if we've reached the maximum number of inputs
        if (currentInputs.length >= MAX_INPUTS) {
            button.disabled = true;
            return;
        }
        
        // Add a new input field
        const newInput = createFileInput(containerId);
        fileInputsDiv.appendChild(newInput);
        
        // Disable the button if we've reached the maximum
        if (currentInputs.length + 1 >= MAX_INPUTS) {
            button.disabled = true;
        }
    }
    
    // Add event listeners to all "Add URL" buttons
    const addUrlButtons = document.querySelectorAll('.add-url-btn');
    addUrlButtons.forEach(button => {
        button.addEventListener('click', handleAddUrlClick);
    });
    
    // Add event listeners to all "Add File" buttons
    const addFileButtons = document.querySelectorAll('.add-file-btn');
    addFileButtons.forEach(button => {
        button.addEventListener('click', handleAddFileClick);
    });
    
    // Handle file input changes
    const fileInputs = document.querySelectorAll('.file-input');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const label = this.nextElementSibling;
            if (this.files && this.files.length > 0) {
                // Add a class to indicate a file is selected
                this.classList.add('has-file');
                // Update the label text to show the file name
                label.textContent = this.files[0].name;
            } else {
                // Remove the class if no file is selected
                this.classList.remove('has-file');
                // Reset the label text
                label.textContent = 'Vyberte soubor';
            }
        });
    });

    // Function to add name attributes to form elements
    function addNameAttributes() {
        // Add names to URL inputs
        for (let i = 1; i <= 4; i++) {
            const urlInputs = document.querySelectorAll(`#urlContainer${i} .url-input`);
            urlInputs.forEach((input, index) => {
                if (input.value.trim() !== '') {
                    input.name = `url_${i}_${index + 1}`;
                }
            });
        }
        
        // Add names to file inputs
        for (let i = 1; i <= 4; i++) {
            const fileInputs = document.querySelectorAll(`#fileContainer${i} .file-input`);
            fileInputs.forEach((input, index) => {
                if (input.files && input.files.length > 0) {
                    input.name = `file_${i}_${index + 1}`;
                }
            });
        }
    }

    // Handle form submission
    const form = document.getElementById('uploadForm');
    form.addEventListener('submit', function(e) {
        e.preventDefault(); // Prevent default form submission
        
        // Add name attributes to form elements before submission
        addNameAttributes();
        
        // Create FormData object
        const formData = new FormData(form);
        
        // Show loading state
        const submitButton = document.getElementById('submitButton');
        const originalText = submitButton.innerHTML;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Odesílám...';
        submitButton.disabled = true;
        
        // Send data to server
        fetch('/submit', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Data byla úspěšně odeslána!');
                console.log('Server response:', data);
                // Optionally reset form
                // form.reset();
            } else {
                alert('Chyba při odesílání: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Chyba při odesílání dat na server.');
        })
        .finally(() => {
            // Restore button state
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        });
    });
    
    // Add paste handler for URL inputs
    document.addEventListener('paste', function(e) {
        // Check if the target is a URL input
        if (e.target.classList.contains('url-input')) {
            const input = e.target;
            const container = input.closest('.url-input-container');
            const urlInputsDiv = container.querySelector('.url-inputs');
            const addButton = container.querySelector('.add-url-btn');
            const currentInputs = urlInputsDiv.querySelectorAll('.url-input');
            
            // Get the pasted text
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');
            
            // Split the pasted text by newlines, commas, or spaces
            const urls = pastedText.split(/[\n,\s]+/).filter(url => url.trim() !== '');
            
            // If there's only one URL or we're at max capacity, let the default paste happen
            if (urls.length <= 1 || currentInputs.length >= MAX_INPUTS) {
                return;
            }
            
            // Prevent the default paste
            e.preventDefault();
            
            // Fill the current input with the first URL
            input.value = urls[0];
            
            // Create new inputs for the remaining URLs (up to the maximum)
            for (let i = 1; i < urls.length && currentInputs.length + i < MAX_INPUTS; i++) {
                const newInput = createUrlInput();
                newInput.value = urls[i];
                urlInputsDiv.appendChild(newInput);
            }
            
            // Disable the add button if we've reached the maximum
            if (urlInputsDiv.querySelectorAll('.url-input').length >= MAX_INPUTS) {
                addButton.disabled = true;
            }
        }
    });
});
