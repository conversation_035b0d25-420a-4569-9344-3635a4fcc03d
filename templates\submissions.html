<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Odeslaná data - Upload System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .submission-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .submission-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .submission-id {
            font-family: monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        .category-section {
            margin-bottom: 15px;
        }
        .category-title {
            color: #4a90e2;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .url-list, .file-list {
            list-style: none;
            padding-left: 20px;
        }
        .url-list li, .file-list li {
            margin-bottom: 3px;
            word-break: break-all;
        }
        .file-info {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
        }
        .back-button {
            display: inline-block;
            margin-bottom: 20px;
            padding: 10px 20px;
            background-color: #4a90e2;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.3s;
        }
        .back-button:hover {
            background-color: #3a7bc8;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Odeslaná data</h1>
            <p>Přehled všech odeslaných formulářů</p>
        </header>

        <a href="/" class="back-button">
            <i class="fas fa-arrow-left"></i> Zpět na formulář
        </a>

        {% if submissions %}
            {% for submission in submissions %}
            <div class="submission-item">
                <div class="submission-header">
                    <h3>Odeslání z {{ submission.timestamp[:19].replace('T', ' ') }}</h3>
                    <span class="submission-id">ID: {{ submission.submission_id }}</span>
                </div>

                {% if submission.urls %}
                <h4>URL adresy:</h4>
                {% for url_section in submission.urls %}
                    {% if url_section.urls %}
                    <div class="category-section">
                        <div class="category-title">{{ url_section.category }}</div>
                        <ul class="url-list">
                            {% for url in url_section.urls %}
                            <li><a href="{{ url }}" target="_blank">{{ url }}</a></li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                {% endfor %}
                {% endif %}

                {% if submission.files %}
                <h4>Soubory:</h4>
                {% for file_section in submission.files %}
                    {% if file_section.files %}
                    <div class="category-section">
                        <div class="category-title">{{ file_section.category }}</div>
                        <ul class="file-list">
                            {% for file in file_section.files %}
                            <li>
                                <i class="fas fa-file"></i> {{ file.original_name }}
                                <span class="file-info">
                                    ({{ "%.1f"|format(file.file_size / 1024) }} KB,
                                    {{ file.file_type }},
                                    uloženo v paměti)
                                </span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                {% endfor %}
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <div class="submission-item">
                <p>Zatím nebyla odeslána žádná data.</p>
            </div>
        {% endif %}
    </div>
</body>
</html>
