# Upload System - Flask Web Application

Webová aplikace pro nahrávání URL adres a souborů do systému DA, vytvořená pomocí Flask frameworku pro Python.

## Požadavky

- Python 3.7 nebo novější
- Windows Server 2019 (nebo jiný Windows systém)

## Instalace a spuštění

### Automatické spuštění (doporučeno)

1. Stáhněte všechny soubory do složky na serveru
2. Spusťte soubor `start_server.bat` dvojklikem
3. Server se automaticky spustí na adrese `http://localhost:8087`

### Manuální spuštění

1. Otevřete příkazový řádek (cmd) ve složce s aplikací
2. Nainstalujte závislosti:
   ```
   pip install -r requirements.txt
   ```
3. Spusťte server:
   ```
   python app.py
   ```

## Použití

### Hlavní formulář
- Otevřete webový prohlížeč a přejděte na `http://localhost:8087`
- <PERSON><PERSON><PERSON><PERSON> obsahuje:
  - **4 sekce pro URL adresy** - kaž<PERSON><PERSON> s rozbalovacím polem pro kategorii a poli pro URL adresy
  - **4 sekce pro soubory** - každá s rozbalovacím polem pro kategorii a poli pro nahrání souborů
  - Možnost přidat další pole pomocí tlačítek "Přidat další URL" nebo "Přidat další soubor"

### Kategorie
Dostupné kategorie:
- **INZ** - Inzerce
- **OPU** - Opuštěné
- **REL** - Relace
- **VEX** - Vexace
- **DINF** - Dezinformace

### Zobrazení odeslaných dat
- Přejděte na `http://localhost:8087/submissions`
- Zobrazí se přehled všech odeslaných formulářů s časovými razítky

## Struktura souborů

```
da-upload-page/
├── app.py                 # Hlavní Flask aplikace
├── requirements.txt       # Python závislosti
├── start_server.bat      # Batch soubor pro spuštění
├── README.md             # Tento soubor
├── templates/            # HTML šablony
│   ├── index.html        # Hlavní formulář
│   └── submissions.html  # Zobrazení odeslaných dat
├── static/               # Statické soubory
│   ├── styles.css        # CSS styly
│   └── script.js         # JavaScript
├── uploads/              # Složka pro nahrané soubory (vytvoří se automaticky)
└── data/                 # Složka pro JSON data (vytvoří se automaticky)
```

## Funkce

### URL adresy
- Validace URL adres před odesláním
- Možnost vložit více URL najednou (automatické rozdělení)
- Dynamické přidávání polí (max. 10 na sekci)

### Soubory
- Podporované formáty: txt, pdf, png, jpg, jpeg, gif, doc, docx, xls, xlsx, zip, rar
- Maximální velikost souboru: 16 MB
- Bezpečné ukládání s unikátními názvy
- Zobrazení názvu souboru po výběru

### Data
- Všechna odeslaná data se ukládají do JSON souborů ve složce `data/`
- Nahrané soubory se ukládají do složky `uploads/`
- Každé odeslání má unikátní ID pro identifikaci

## Konfigurace serveru

Pro produkční použití na Windows Server 2019:

1. **Firewall**: Otevřete port 8087 ve Windows Firewall
2. **Síťový přístup**: Server je již nakonfigurován pro přístup z celé sítě:
   ```python
   app.run(host='0.0.0.0', port=8087, debug=False)
   ```
3. **HTTPS**: Pro produkční použití doporučujeme použít HTTPS proxy (např. nginx)

## Řešení problémů

### Python není nainstalován
- Stáhněte Python z https://python.org
- Při instalaci zaškrtněte "Add Python to PATH"

### Port 8087 je obsazen
- Změňte port v `app.py` na jiný (např. 8080):
  ```python
  app.run(host='0.0.0.0', port=8080, debug=False)
  ```

### Problémy s oprávněními
- Spusťte příkazový řádek jako administrátor
- Ujistěte se, že máte oprávnění k zápisu do složky aplikace

## 📝 Přístup k datům

- **Webové rozhraní**: `http://localhost:8087/submissions` - zobrazení všech odeslaných dat
- **JSON soubory**: Složka `data/` - každé odeslání má vlastní JSON soubor
- **Nahrané soubory**: Složka `uploads/` - všechny nahrané soubory

## Kontakt

Pro technickou podporu nebo dotazy kontaktujte správce systému.
