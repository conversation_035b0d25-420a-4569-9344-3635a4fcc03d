# Upload System - Flask Web Application

Webová aplikace pro nahrávání URL adres a souborů do systému DA, vytvořená pomocí Flask frameworku pro Python.

## Požadavky

- Python 3.7 nebo novější
- Windows Server 2019 (nebo jiný Windows systém)

## Instalace a spuštění

### Automatické spuštění (doporučeno)

1. Stáhněte všechny soubory do složky na serveru
2. Spusťte soubor `start_server.bat` dvojklikem
3. Server se automaticky spustí na adrese `http://localhost:8087`

### Manuální spuštění

1. Otevřete příkazový řádek (cmd) ve složce s aplikací
2. Nainstalujte závislosti:
   ```
   pip install -r requirements.txt
   ```
3. Spusťte server:
   ```
   python app.py
   ```

## Použití

### Hlavn<PERSON> formul<PERSON>ř
- Otevřete webový prohlížeč a přejděte na `http://localhost:8087`
- **S PU parametrem**: `http://localhost:8087/9536863` (kde 9536863 je číslo PU)
- Formulář obsahuje:
  - **PU (Project Unit)** - povinný parametr pro identifikaci projektu
  - **4 sekce pro URL adresy** - každá s rozbalovacím polem pro kategorii a poli pro URL adresy
  - **4 sekce pro soubory** - každá s rozbalovacím polem pro kategorii a poli pro nahrání souborů
  - Možnost přidat další pole pomocí tlačítek "Přidat další URL" nebo "Přidat další soubor"

### Kategorie
Dostupné kategorie:
- **INZ** - Inzerce
- **OPU** - Opuštěné
- **REL** - Relace
- **VEX** - Vexace
- **DINF** - Dezinformace

### Zobrazení odeslaných dat
- Přejděte na `http://localhost:8087/submissions`
- Zobrazí se přehled všech odeslaných formulářů s časovými razítky

### Stránka potvrzení
- Po úspěšném odeslání se automaticky zobrazí stránka potvrzení
- Obsahuje statistiky o nahraných datech (počet URL, souborů, kategorií)
- Zobrazuje detaily o odeslání (PU, ID, čas)
- Přehled všech nahraných kategorií s počty
- Tlačítka pro nahrání dalších dat nebo zobrazení všech odeslání

## Struktura souborů

```
da-upload-page/
├── app.py                 # Hlavní Flask aplikace
├── requirements.txt       # Python závislosti
├── start_server.bat      # Batch soubor pro spuštění
├── README.md             # Tento soubor
├── templates/            # HTML šablony
│   ├── index.html        # Hlavní formulář
│   └── submissions.html  # Zobrazení odeslaných dat
├── static/               # Statické soubory
│   ├── styles.css        # CSS styly
│   └── script.js         # JavaScript
└── data/                 # Složka pro JSON data (vytvoří se automaticky)
```

## Funkce

### URL adresy
- Validace URL adres před odesláním
- Možnost vložit více URL najednou (automatické rozdělení)
- Dynamické přidávání polí (max. 10 na sekci)
- **Kontrola dat**: Musí být zadána alespoň jedna URL adresa nebo soubor

### Soubory
- Podporované formáty: txt, pdf, png, jpg, jpeg, gif, doc, docx, xls, xlsx, zip, rar
- Maximální velikost souboru: 16 MB
- **Ukládání pouze do paměti** - soubory se neukládají na disk
- Zobrazení názvu souboru po výběru
- Automatické vymazání po odeslání formuláře

### Data
- Všechna odeslaná data se ukládají do JSON souborů ve složce `data/`
- **Soubory se ukládají přímo do JSON** (base64 kódování) - žádné soubory na disku
- Každé odeslání má unikátní ID pro identifikaci
- **PU parametr** se ukládá s každým odesláním
- **Automatické vymazání formuláře** po úspěšném odeslání

### PU (Project Unit) parametr
- **Zadání v URL**: `http://localhost:8087/123456` - PU se automaticky načte z URL
- **Bez PU v URL**: Při submitu se zobrazí dialog pro zadání PU
- **Povinnost**: PU je povinný parametr - bez něj nelze data odeslat
- **Potvrzení**: Pokud se PU zadává ručně, zobrazí se potvrzovací dialog

### Validace dat
- **Povinná data**: Musí být zadána alespoň jedna URL adresa nebo vybrán alespoň jeden soubor
- **Chybová zpráva**: "Nejsou žádná data k nahrání. Zadejte alespoň jednu URL adresu nebo vyberte soubor."
- **Kontrola na frontendu i backendu**: Dvojitá validace pro bezpečnost

## Konfigurace serveru

Pro produkční použití na Windows Server 2019:

1. **Firewall**: Otevřete port 8087 ve Windows Firewall
2. **Síťový přístup**: Server je již nakonfigurován pro přístup z celé sítě:
   ```python
   app.run(host='0.0.0.0', port=8087, debug=False)
   ```
3. **HTTPS**: Pro produkční použití doporučujeme použít HTTPS proxy (např. nginx)

## Řešení problémů

### Python není nainstalován
- Stáhněte Python z https://python.org
- Při instalaci zaškrtněte "Add Python to PATH"

### Port 8087 je obsazen
- Změňte port v `app.py` na jiný (např. 8080):
  ```python
  app.run(host='0.0.0.0', port=8080, debug=False)
  ```

### Problémy s oprávněními
- Spusťte příkazový řádek jako administrátor
- Ujistěte se, že máte oprávnění k zápisu do složky aplikace

## 📝 Přístup k datům

- **Webové rozhraní**: `http://localhost:8087/submissions` - zobrazení všech odeslaných dat
- **JSON soubory**: Složka `data/` - každé odeslání má vlastní JSON soubor s URL adresami i soubory (base64)

## Kontakt

Pro technickou podporu nebo dotazy kontaktujte správce systému.
